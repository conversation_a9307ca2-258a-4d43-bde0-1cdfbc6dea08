# ADR-008: Block Asset Management Architecture

## Status

**Accepted** - Implementation in progress

## Context

WordPress blocks require a different asset management approach compared to traditional scripts and styles. While our existing asset system is optimized for performance through deferred loading, blocks have unique requirements:

1. **Early Registration**: Blocks must be registered early (typically on `init`) for WordPress editor functionality
2. **Conditional Assets**: Block assets should only load when blocks are present on the page
3. **WordPress Integration**: Must integrate seamlessly with WordPress's `register_block_type()` API
4. **Asset Lifecycle**: Block assets have different scopes (editor, frontend, dynamic) with different loading requirements

## Decision

We will extend our existing asset management system with block-aware functionality that **inverts the default behavior** compared to traditional assets:

### **Architectural Pattern Inversion**

| Component                   | Default Behavior       | Exception Cases                           | Rationale                          |
| --------------------------- | ---------------------- | ----------------------------------------- | ---------------------------------- |
| **Assets (Scripts/Styles)** | Deferred loading       | Immediate loading (`enqueue_immediate()`) | Performance optimization           |
| **Blocks**                  | Immediate registration | Deferred registration                     | WordPress architecture requirement |

### **Core Components**

1. **BlockAssetTrait**: Provides block-aware asset management functionality
2. **BlockAssetManager**: Extends `AssetEnqueueBaseAbstract` with block integration
3. **BlockRegistrar**: Handles WordPress block registration with asset integration

## Implementation Architecture

### **1. Block Asset Registration**

```php
// Block assets are registered with scope-specific loading
$block_manager->register_block_assets('my-plugin/hero-block', [
    'editor_scripts' => [
        [
            'handle' => 'hero-editor',
            'src' => 'blocks/hero/editor.js',
            'deps' => ['wp-blocks', 'wp-element']
            // Loads in editor context only
        ]
    ],
    'frontend_scripts' => [
        [
            'handle' => 'hero-frontend',
            'src' => 'blocks/hero/frontend.js',
            'condition' => function() use ($block_name) {
                return $this->_is_block_present($block_name);
            }
            // Loads only when block is present
        ]
    ],
    'dynamic_scripts' => [
        [
            'handle' => 'hero-dynamic',
            'src' => 'blocks/hero/dynamic.js',
            'hook' => 'render_block'
            // Loads during block render
        ]
    ]
]);
```

### **2. WordPress Hook Integration**

```php
// BlockAssetManager hooks into WordPress lifecycle
public function load(): void {
    // Early block detection for conditional loading
    $this->_do_add_action('wp', [$this, 'detect_block_presence'], 5);

    // Standard asset processing with block awareness
    $this->_do_add_action('wp_enqueue_scripts', [$this, 'stage'], 10);
    $this->_do_add_action('wp_enqueue_scripts', [$this, 'enqueue_immediate'], 20);

    // Editor-specific assets
    $this->_do_add_action('enqueue_block_editor_assets', [$this, 'enqueue_editor_assets'], 10);

    // Dynamic block integration
    $this->_do_add_filter('render_block', [$this, '_maybe_enqueue_dynamic_assets'], 10, 2);
}
```

### **3. Block Registration Integration**

```php
// BlockRegistrar provides unified block + asset registration
$block_registrar->add([
    [
        'block_name' => 'my-plugin/hero-block',
        // Default: immediate registration on 'init'
        'condition' => function() { return current_user_can('edit_posts'); },
        'block_config' => [
            'render_callback' => [$this, 'render_hero']
        ],
        'assets' => [/* asset definitions */]
    ],
    [
        'block_name' => 'my-plugin/admin-block',
        'hook' => 'admin_init',  // Exception: deferred registration
        'condition' => function() { return is_admin(); },
        'block_config' => [/* ... */],
        'assets' => [/* ... */]
    ]
]);
```

## Asset Loading Lifecycle

### **1. Block Presence Detection**

```php
// Early detection on 'wp' hook for static blocks
public function detect_block_presence(): array {
    if ($this->blocks_detected) {
        return $this->detected_blocks;
    }

    global $post;
    if (!$post || !has_blocks($post->post_content)) {
        return [];
    }

    $blocks = parse_blocks($post->post_content);
    $this->detected_blocks = $this->_extract_block_names($blocks);
    $this->blocks_detected = true;

    return $this->detected_blocks;
}
```

### **2. Conditional Asset Loading**

```php
// Assets load only when their block is present
foreach ($asset_config['frontend_scripts'] ?? [] as $script) {
    $script['condition'] = function() use ($block_name) {
        return $this->_is_block_present($block_name);
    };
    $this->add_assets([$script], AssetType::Script);
}
```

### **3. WordPress Integration Approaches**

#### **Approach A: Filter-Based Enhancement (Recommended)**

```php
// Enhance existing register_block_type() calls
$this->_do_add_filter('register_block_type_args', [$this, '_integrate_block_assets'], 10, 2);

public function _integrate_block_assets(array $args, string $block_name): array {
    if (isset($this->block_assets[$block_name])) {
        // Map our asset handles to WordPress expected format
        $args['editor_script'] = $this->block_assets[$block_name]['editor_scripts'][0]['handle'] ?? null;
        $args['script'] = $this->block_assets[$block_name]['frontend_scripts'][0]['handle'] ?? null;
        // ... map other asset types
    }
    return $args;
}
```

#### **Approach B: Direct Registration**

```php
// Direct block registration with asset integration
public function register_deferred_blocks(): void {
    foreach ($this->registered_blocks as $block_name => $config) {
        // Register assets first
        if (isset($config['assets'])) {
            $this->register_block_assets($block_name, $config['assets']);
        }

        // Register block with WordPress
        register_block_type($block_name, $config['block_config']);
    }
}
```

## Usage Patterns

### **1. Simple Block with Assets**

```php
$block_manager->register_block_assets('my-plugin/simple-block', [
    'editor_scripts' => [
        ['handle' => 'simple-editor', 'src' => 'blocks/simple/editor.js']
    ],
    'frontend_styles' => [
        ['handle' => 'simple-style', 'src' => 'blocks/simple/style.css']
    ]
]);
```

### **2. Dynamic Block with Conditional Loading**

```php
$block_manager->register_block_assets('my-plugin/dynamic-block', [
    'dynamic_scripts' => [
        [
            'handle' => 'dynamic-script',
            'src' => 'blocks/dynamic/script.js',
            'hook' => 'render_block',
            'condition' => function() { return !is_admin(); }
        ]
    ]
]);
```

### **3. Block Groups with Shared Dependencies**

```php
$block_manager->register_block_group('my-plugin-blocks', [
    'my-plugin/block-a',
    'my-plugin/block-b',
    'my-plugin/block-c'
], [
    'shared_scripts' => [
        ['handle' => 'shared-lib', 'src' => 'blocks/shared/lib.js']
    ]
]);
```

## Benefits

1. **Performance**: Assets load only when blocks are present
2. **Consistency**: Familiar API patterns from existing asset system
3. **Flexibility**: Supports immediate and deferred block registration
4. **Integration**: Seamless WordPress block registration enhancement
5. **Maintainability**: Extends proven asset management architecture

## Trade-offs

1. **Complexity**: Additional abstraction layer over WordPress block registration
2. **Learning Curve**: Developers must understand inverted default behavior
3. **Documentation**: Requires clear explanation of asset vs block patterns

## Future Considerations

1. **Block Asset Dependencies**: Automatic dependency resolution between blocks
2. **Asset Bundling**: Combine related block assets for performance
3. **Cache Integration**: Block presence caching across requests
4. **Development Tools**: Debug utilities for block asset loading

---

**Related ADRs**: ADR-001 (Asset Deferral), ADR-004 (Asset Queues), ADR-007 (Asset Replacement)
