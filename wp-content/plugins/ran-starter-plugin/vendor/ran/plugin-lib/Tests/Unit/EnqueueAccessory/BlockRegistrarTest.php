<?php
declare(strict_types=1);

namespace Ran\PluginLib\Tests\Unit\EnqueueAccessory;

use Mockery;
use Ran\PluginLib\Config\ConfigInterface;
use Ran\PluginLib\Tests\Unit\PluginLibTestCase;
use Ran\PluginLib\EnqueueAccessory\BlockRegistrar;
use Ran\PluginLib\Util\CollectingLogger;
use WP_Mock;

/**
 * Class BlockRegistrarTest
 *
 * @package Ran\PluginLib\Tests\Unit\EnqueueAccessory
 *
 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar
 */
class BlockRegistrarTest extends PluginLibTestCase {
	/** @var BlockRegistrar */
	protected $instance;

	/** @var Mockery\MockInterface|ConfigInterface */
	protected $config_mock;

	/** @var CollectingLogger */
	protected $logger;

	/**
	 * Set up test environment.
	 */
	public function setUp(): void {
		parent::setUp();

		$this->logger = new CollectingLogger();

		$this->config_mock = Mockery::mock(ConfigInterface::class);
		$this->config_mock->shouldReceive('get_logger')->andReturn($this->logger)->byDefault();

		$this->instance = new BlockRegistrar($this->config_mock);
	}

	/**
	 * Clean up test environment.
	 */
	public function tearDown(): void {
		parent::tearDown();
		Mockery::close();
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::__construct
	 */
	public function test_constructor_sets_config(): void {
		$this->assertInstanceOf(BlockRegistrar::class, $this->instance);
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::add
	 */
	public function test_add_stores_block_definitions(): void {
		$blocks = array(
			array(
				'block_name'   => 'my-plugin/hero-block',
				'hook'         => 'init',
				'priority'     => 10,
				'block_config' => array(
					'render_callback' => 'my_render_callback',
				),
				'assets' => array(
					'frontend_scripts' => array(
						array(
							'handle' => 'hero-block-frontend',
							'src'    => 'hero-block.js',
						),
					),
				),
			),
		);

		$result = $this->instance->add($blocks);

		$this->assertSame($this->instance, $result);
		$this->assertContains('Adding block \'my-plugin/hero-block\' for registration', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::add
	 */
	public function test_add_skips_invalid_definitions(): void {
		$blocks = array(
			array(
				// Missing block_name
				'hook' => 'init',
			),
		);

		$result = $this->instance->add($blocks);

		$this->assertSame($this->instance, $result);
		$this->assertContains('Block definition missing \'block_name\'', $this->logger->get_warning_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::load
	 */
	public function test_load_registers_wordpress_hooks(): void {
		// Add a block first
		$this->instance->add(array(
			array(
				'block_name' => 'my-plugin/test-block',
				'hook'       => 'init',
			),
		));

		// Mock WordPress functions
		WP_Mock::expectActionAdded('init', Mockery::type('callable'), 10);
		WP_Mock::expectFilterAdded('register_block_type_args', array($this->instance, '_integrate_block_assets'), 10);
		WP_Mock::expectActionAdded('wp', array($this->instance, 'detect_block_presence'), 5);
		WP_Mock::expectActionAdded('wp_enqueue_scripts', array($this->instance, 'stage'), 10);
		WP_Mock::expectActionAdded('wp_enqueue_scripts', array($this->instance, 'enqueue_immediate'), 20);
		WP_Mock::expectActionAdded('enqueue_block_editor_assets', array($this->instance, 'enqueue_editor_assets'), 10);
		WP_Mock::expectFilterAdded('render_block', array($this->instance, '_maybe_enqueue_dynamic_assets'), 10);

		$this->instance->load();

		$this->assertContains('Registering action for hook \'init\'', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_integrate_block_assets
	 */
	public function test_integrate_block_assets_maps_handles(): void {
		// Register block assets first
		$this->instance->register_block_assets('my-plugin/test-block', array(
			'editor_scripts' => array(
				array('handle' => 'test-editor-script'),
			),
			'frontend_scripts' => array(
				array('handle' => 'test-frontend-script'),
			),
			'editor_styles' => array(
				array('handle' => 'test-editor-style'),
			),
			'frontend_styles' => array(
				array('handle' => 'test-frontend-style'),
			),
		));

		$args   = array();
		$result = $this->instance->_integrate_block_assets($args, 'my-plugin/test-block');

		$expected = array(
			'editor_script' => 'test-editor-script',
			'script'        => 'test-frontend-script',
			'editor_style'  => 'test-editor-style',
			'style'         => 'test-frontend-style',
		);

		$this->assertEquals($expected, $result);
		$this->assertContains('Integrating assets for block \'my-plugin/test-block\'', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_integrate_block_assets
	 */
	public function test_integrate_block_assets_returns_unchanged_for_unknown_block(): void {
		$args   = array('existing' => 'value');
		$result = $this->instance->_integrate_block_assets($args, 'unknown/block');

		$this->assertEquals($args, $result);
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::stage
	 */
	public function test_stage_processes_both_asset_types(): void {
		$result = $this->instance->stage();

		$this->assertSame($this->instance, $result);
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::enqueue_immediate
	 */
	public function test_enqueue_immediate_processes_both_asset_types(): void {
		$result = $this->instance->enqueue_immediate();

		$this->assertSame($this->instance, $result);
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::enqueue_editor_assets
	 */
	public function test_enqueue_editor_assets_processes_editor_assets(): void {
		// Register block assets first
		$this->instance->register_block_assets('my-plugin/test-block', array(
			'editor_scripts' => array(
				array(
					'handle' => 'test-editor-script',
					'src'    => 'editor.js',
				),
			),
			'editor_styles' => array(
				array(
					'handle' => 'test-editor-style',
					'src'    => 'editor.css',
				),
			),
		));

		$result = $this->instance->enqueue_editor_assets();

		$this->assertSame($this->instance, $result);
		$this->assertContains('Processing editor assets for registered blocks', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_maybe_enqueue_dynamic_assets
	 */
	public function test_maybe_enqueue_dynamic_assets_processes_known_blocks(): void {
		// Register block assets first
		$this->instance->register_block_assets('my-plugin/test-block', array(
			'dynamic_scripts' => array(
				array(
					'handle' => 'test-dynamic-script',
					'src'    => 'dynamic.js',
				),
			),
		));

		$block_content = '<div>Test content</div>';
		$block         = array('blockName' => 'my-plugin/test-block');

		$result = $this->instance->_maybe_enqueue_dynamic_assets($block_content, $block);

		$this->assertEquals($block_content, $result);
		$this->assertContains('Enqueuing dynamic assets for block \'my-plugin/test-block\'', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_maybe_enqueue_dynamic_assets
	 */
	public function test_maybe_enqueue_dynamic_assets_ignores_unknown_blocks(): void {
		$block_content = '<div>Test content</div>';
		$block         = array('blockName' => 'unknown/block');

		$result = $this->instance->_maybe_enqueue_dynamic_assets($block_content, $block);

		$this->assertEquals($block_content, $result);
		// Should not contain any debug messages about enqueuing dynamic assets
		$debug_messages = $this->logger->get_debug_messages();
		$this->assertEmpty(array_filter($debug_messages, function($message) {
			return strpos($message, 'Enqueuing dynamic assets') !== false;
		}));
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_register_single_block
	 */
	public function test_register_single_block_with_condition(): void {
		$block_definition = array(
			'block_name' => 'my-plugin/conditional-block',
			'condition'  => function() {
				return false;
			}, // Condition fails
			'block_config' => array(),
		);

		// Use reflection to call the protected method
		$reflection = new \ReflectionClass($this->instance);
		$method     = $reflection->getMethod('_register_single_block');
		$method->setAccessible(true);

		$method->invoke($this->instance, $block_definition);

		$this->assertContains('Condition failed for block \'my-plugin/conditional-block\'', $this->logger->get_debug_messages());
	}

	/**
	 * @test
	 * @covers \Ran\PluginLib\EnqueueAccessory\BlockRegistrar::_register_single_block
	 */
	public function test_register_single_block_calls_wordpress_function(): void {
		$block_definition = array(
			'block_name'   => 'my-plugin/test-block',
			'block_config' => array(
				'render_callback' => 'my_render_callback',
			),
		);

		// Mock the WordPress function
		WP_Mock::userFunction('register_block_type')
			->once()
			->with('my-plugin/test-block', Mockery::type('array'))
			->andReturn(true);

		// Use reflection to call the protected method
		$reflection = new \ReflectionClass($this->instance);
		$method     = $reflection->getMethod('_register_single_block');
		$method->setAccessible(true);

		$method->invoke($this->instance, $block_definition);

		$this->assertContains('Registering block \'my-plugin/test-block\' with WordPress', $this->logger->get_debug_messages());
	}
}
